import mysql.connector
from mysql.connector import Error

try:
    # Replace these with your MySQL username and password
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='edenyehualashet@123'
    )

    if connection.is_connected():
        cursor = connection.cursor()
        cursor.execute("CREATE DATABASE IF NOT EXISTS alx_book_store")
        print("Database 'alx_book_store' created successfully!")

except Error as e:
    print(f"Error: {e}")

finally:
    if 'cursor' in locals():
        cursor.close()
    if 'connection' in locals() and connection.is_connected():
        connection.close()
